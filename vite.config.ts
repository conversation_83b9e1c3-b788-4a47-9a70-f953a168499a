import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { securityHeadersPlugin } from './src/lib/security/security-headers';

// Add a lightweight preview proxy so `vite preview` can also hit TempStick via /api/tempstick/*
function tempstickPreviewProxyPlugin(env: Record<string, string>) {
  return {
    name: 'tempstick-preview-proxy',
    // Only used in `vite preview`
    configurePreviewServer(server: any) {
      server.middlewares.use(async (req: any, res: any, next: any) => {
        try {
          const url = req.url as string | undefined;
          if (!url || !url.startsWith('/api/tempstick/')) return next();

          const tail = url.replace(/^\/api\/tempstick\/?/, '');
          const base = 'https://tempstickapi.com/api/v1';
          const target = `${base}/${tail}`;
          const apiKey = process.env.VITE_TEMPSTICK_API_KEY || process.env.TEMPSTICK_API_KEY || env?.VITE_TEMPSTICK_API_KEY;
          if (!apiKey) {
            res.statusCode = 500;
            res.setHeader('Content-Type', 'application/json');
            return res.end(JSON.stringify({ error: 'Missing TempStick API key (VITE_TEMPSTICK_API_KEY)' }));
          }

          const headers: Record<string, string> = {
            'X-API-KEY': apiKey,
            'User-Agent': '',
            'Accept': 'application/json',
          };
          const method = req.method || 'GET';
          const isBodyless = method === 'GET' || method === 'HEAD';
          if (!isBodyless) headers['Content-Type'] = (req.headers['content-type'] as string) || 'application/json';

          let body: any = undefined;
          if (!isBodyless) {
            body = await new Promise<string>((resolve) => {
              let data = '';
              req.on('data', (chunk: any) => (data += chunk));
              req.on('end', () => resolve(data));
            });
          }

          const upstream = await fetch(target, { method, headers, body });
          const text = await upstream.text();
          res.statusCode = upstream.status;
          res.setHeader('Content-Type', upstream.headers.get('content-type') || 'application/json');
          try {
            const json = JSON.parse(text);
            return res.end(JSON.stringify(json));
          } catch {
            return res.end(text);
          }
        } catch (err) {
          res.statusCode = 502;
          res.setHeader('Content-Type', 'application/json');
          return res.end(JSON.stringify({ error: 'TempStick preview proxy error' }));
        }
      });
    },
  } as any;
}

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isProd = mode === 'production';
  
  return {
    plugins: [
      react(),
      securityHeadersPlugin(),
      tempstickPreviewProxyPlugin(env),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        react: path.resolve(__dirname, './node_modules/react'),
        'react-dom': path.resolve(__dirname, './node_modules/react-dom'),
      },
      dedupe: ['react', 'react-dom'],
    },
    
    server: {
      port: 5177,
      strictPort: true,
      host: true,
      proxy: {
        // Local voice assistant API (Express server on 3001)
        '/api/voice': {
          target: 'http://localhost:3001',
          changeOrigin: true,
        },
        '/api/voice-realtime-check': {
          target: 'http://localhost:3001',
          changeOrigin: true,
        },
        // Ephemeral token for WebRTC sessions
        '/api/voice/ephemeral-token': {
          target: 'http://localhost:3001',
          changeOrigin: true,
        },
        // WebSocket relay for OpenAI Realtime API
        '/api/realtime-relay': {
          target: 'http://localhost:3001',
          changeOrigin: true,
          ws: true,
        },
        // Proxy for OpenAI Realtime calls negotiation (SDP) via local express to avoid CORS
        '/api/openai': {
          target: 'http://localhost:3001',
          changeOrigin: true,
        },
        '/api/tempstick': {
          target: env.VITE_TEMPSTICK_API_URL || 'https://tempstickapi.com/api/v1',
          changeOrigin: true,
          rewrite: (path) => {
            const rewritten = path.replace(/^\/api\/tempstick/, '');
            console.log(`[TempStick Proxy] Rewriting: ${path} -> ${rewritten || '/'}`);
            return rewritten || '/';
          },
          ws: false,
          configure: (proxy) => {
            const resolveApiKey = () =>
              process.env.VITE_TEMPSTICK_API_KEY
              || process.env.TEMPSTICK_API_KEY
              || env.VITE_TEMPSTICK_API_KEY;

            proxy.on('error', (err) => {
              console.error('[TempStick Proxy] Error', err);
            });

            proxy.on('proxyReq', (proxyReq, req) => {
              const apiKey = resolveApiKey();
              if (apiKey) {
                proxyReq.setHeader('X-API-KEY', apiKey);
                const redacted = apiKey.length > 8
                  ? `${apiKey.slice(0, 4)}…${apiKey.slice(-2)}`
                  : '***';
                console.log('[TempStick Proxy] Injected API key for', req.method, req.url, `(key: ${redacted})`);
              } else {
                console.warn('[TempStick Proxy] Missing VITE_TEMPSTICK_API_KEY for', req.method, req.url);
              }

              proxyReq.setHeader('User-Agent', '');
              proxyReq.setHeader('Accept', 'application/json');

              // Mirror preview proxy behaviour for content-type passthrough
              if (!proxyReq.getHeader('Content-Type') && req.headers['content-type']) {
                proxyReq.setHeader('Content-Type', req.headers['content-type']);
              }

              console.log('[TempStick Proxy] Request', req.method, req.url, '->', proxyReq.path);
            });

            proxy.on('proxyRes', (proxyRes, req) => {
              console.log('[TempStick Proxy] Response', proxyRes.statusCode, req.url);
            });
          }
        }
      }
    },
    preview: {
      port: 5177,
      strictPort: true,
      host: true
    },
    optimizeDeps: {
      include: ['regenerator-runtime/runtime']
    },
    build: isProd ? {
      target: ['es2020', 'chrome80', 'safari13'],
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn'],
          passes: 3,
          pure_getters: true,
          unsafe_comps: true,
          unsafe_math: true,
          unsafe_methods: true
        },
        mangle: {
          safari10: true,
          properties: {
            regex: /^_/
          }
        },
        format: {
          comments: false
        }
      },
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Only split chunks that actually exist and are large enough
            if (id.includes('node_modules')) {
              // Major vendor chunks
              if (id.includes('@supabase')) {
                return 'supabase';
              }
              if (id.includes('react') || id.includes('react-dom')) {
                return 'react';
              }
              if (id.includes('@radix-ui')) {
                return 'ui-components';
              }
              if (id.includes('date-fns') || id.includes('react-calendar')) {
                return 'date-utils';
              }
              if (id.includes('zod') || id.includes('react-hook-form')) {
                return 'forms';
              }
              if (id.includes('lucide-react')) {
                return 'icons';
              }
              // Only create separate chunks for libraries that are actually large
              if (id.includes('lodash') || id.includes('ramda') || id.includes('axios')) {
                return 'vendor-utils';
              }
              return 'vendor';
            }
            
            // Only split our code if chunks would be meaningful
            if (id.includes('/components/voice/') || id.includes('/services/') && id.includes('Voice')) {
              return 'voice-features';
            }
            if (id.includes('/components/import/') && !id.includes('voice')) {
              return 'import-features';  
            }
            if (id.includes('/components/haccp/') && !id.includes('voice')) {
              return 'haccp-features';
            }
            
            // Keep everything else in main chunk for now
            return undefined;
          },
          chunkFileNames: '[name]-[hash].js'
        }
      },
      chunkSizeWarningLimit: 300,
      assetsInlineLimit: 4096,
      cssCodeSplit: true
    } : undefined
  };
});
