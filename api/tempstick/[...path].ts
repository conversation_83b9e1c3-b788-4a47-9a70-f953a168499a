import type { VercelRequest, VercelResponse } from '@vercel/node';

// Catch-all proxy for TempStick API: /api/tempstick/* -> https://tempstickapi.com/api/v1/*
// Ensures API key stays server-side and avoids direct browser calls to upstream.
export default async function handler(req: VercelRequest, res: VercelResponse) {
  const parseOrigins = (value?: string | null): string[] =>
    value
      ?.split(',')
      .map((origin) => origin.trim())
      .filter((origin) => origin.length > 0)
      ?? [];

  const requestOrigin = Array.isArray(req.headers.origin)
    ? req.headers.origin[0]
    : req.headers.origin;
  const primaryAllowedOrigins = parseOrigins(process.env.TEMPSTICK_ALLOWED_ORIGINS);
  const fallbackAllowedOrigins = parseOrigins(process.env.ALLOWED_ORIGINS);
  const localAllowedOrigins = parseOrigins(process.env.VITE_TEMPSTICK_ALLOWED_ORIGINS);
  const allowedOrigins = primaryAllowedOrigins.length > 0
    ? primaryAllowedOrigins
    : fallbackAllowedOrigins.length > 0
      ? fallbackAllowedOrigins
      : localAllowedOrigins;

  const environment = process.env.VERCEL_ENV ?? process.env.NODE_ENV ?? 'development';
  const isDevelopment = environment === 'development' || environment === 'preview' || environment === 'test';

  const resolvedOrigin = (() => {
    if (requestOrigin && allowedOrigins.includes(requestOrigin)) {
      return requestOrigin;
    }
    if (isDevelopment) {
      return '*';
    }
    return allowedOrigins[0];
  })();

  if (resolvedOrigin) {
    res.setHeader('Access-Control-Allow-Origin', resolvedOrigin);
  }
  res.setHeader('Vary', 'Origin');
  res.setHeader('Access-Control-Allow-Methods', 'GET,POST,PUT,PATCH,DELETE,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-API-KEY');
  if (req.method === 'OPTIONS') return res.status(204).end();

  try {
    const segments = Array.isArray(req.query.path)
      ? (req.query.path as string[])
      : typeof req.query.path === 'string'
        ? [req.query.path]
        : [];

    const qs = req.url?.includes('?') ? req.url.split('?')[1] : '';
    const tail = segments.join('/');
    const base = process.env.TEMPSTICK_API_URL || 'https://tempstickapi.com/api/v1';
    const url = `${base}/${tail}${qs ? `?${qs}` : ''}`;

    const apiKey = process.env.TEMPSTICK_API_KEY || process.env.VITE_TEMPSTICK_API_KEY;
    if (!apiKey) {
      return res
        .status(500)
        .json({ error: 'Missing TempStick API key. Set TEMPSTICK_API_KEY in server env.' });
    }

    const upstreamHeaders: Record<string, string> = {
      'X-API-KEY': apiKey,
      // Per TempStick docs, empty UA is allowed and avoids firewall blocks
      'User-Agent': '',
      'Accept': 'application/json',
    };

    const isBodyless = req.method === 'GET' || req.method === 'HEAD';
    const contentType = (req.headers['content-type'] as string) || 'application/json';
    if (!isBodyless) upstreamHeaders['Content-Type'] = contentType;

    const body = isBodyless ? undefined : (typeof req.body === 'string' ? req.body : JSON.stringify(req.body ?? {}));

    const r = await fetch(url, {
      method: req.method,
      headers: upstreamHeaders,
      body,
    });

    // Mirror upstream status and content type
    const ct = r.headers.get('content-type') || 'application/json';
    res.status(r.status);
    res.setHeader('Content-Type', ct);

    // Stream JSON if possible, otherwise pass-through text
    const text = await r.text();
    try {
      const json = JSON.parse(text);
      return res.json(json);
    } catch {
      return res.send(text);
    }
  } catch (err: any) {
    console.error('TempStick proxy error:', err?.message || err);
    return res.status(502).json({ error: 'TempStick proxy error' });
  }
}
