{"id": "ModernRealtimeVoiceClient_connect_fallback_001", "text": "ModernRealtimeVoiceClient.connect now inspects both error messages and nested error codes for negotiation indicators such as Unknown parameter, negotiation, SDP, ICE, peerConnection. On detection, it rebuilds the session using OpenAIRealtimeWebSocket transport and retries the connection once before surfacing the error.", "tags": ["ModernRealtimeVoiceClient", "connect", "fallback", "transport", "negotiation", "OpenAIRealtimeWebSocket"], "timestamp": "2025-09-28T03:25:00Z"}