# Local development environment variables
# Copy from .env.example and populate any secrets in a non-committed override file if required.

# -----------------------------------------------------------------------------
# CLIENT_VISIBLE
# -----------------------------------------------------------------------------
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=your-local-supabase-anon-key
VITE_API_BASE_URL=http://127.0.0.1:3001
VITE_TEMPSTICK_API_URL=http://127.0.0.1:3001/api/v1
VITE_TEMPSTICK_API_KEY=local-tempstick-api-key
# VITE_TEMPSTICK_WEBHOOK_URL=
VITE_ENVIRONMENT=development
VITE_TEMPSTICK_SYNC_INTERVAL=300000
VITE_FEATURE_DIRECT_REALTIME=false
VITE_FEATURE_USE_WEBRTC=true
VITE_FEATURE_OVERLAY_CLEANUP=false
VITE_FEATURE_OVERLAY_DIAGNOSTIC=false
VITE_FEATURE_BOTTOM_BAR_DEBUG=false
VITE_SKIP_AUTH=false
VITE_REALTIME_MODEL=gpt-4o-realtime-preview-2024-12-17
VITE_REALTIME_VOICE=alloy
VITE_DATADOG_API_KEY=
VITE_DATADOG_APP_KEY=
VITE_SENTRY_DSN=
VITE_SMTP_HOST=
VITE_SMTP_PORT=587
VITE_SMTP_USERNAME=
VITE_SMTP_PASSWORD=
VITE_SMTP_FROM=<EMAIL>
VITE_SMS_PROVIDER=twilio
VITE_TWILIO_ACCOUNT_SID=
VITE_TWILIO_AUTH_TOKEN=
VITE_TWILIO_FROM_NUMBER=
VITE_SLACK_WEBHOOK_URL=
VITE_SLACK_BOT_TOKEN=

# -----------------------------------------------------------------------------
# SERVER_ONLY
# -----------------------------------------------------------------------------
SUPABASE_SERVICE_ROLE_KEY=
OPENAI_API_KEY=
TEMPSTICK_SYNC_USER_ID=
