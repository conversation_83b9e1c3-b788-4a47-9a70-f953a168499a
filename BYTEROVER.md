# CipherHANDBOOK
*Pacific Cloud Seafoods Manager - AI Agent Navigation Guide*

---

## Layer 1: System Overview

### Purpose
Comprehensive seafood inventory management and compliance tracking system with voice-enabled data entry, real-time traceability, and HACCP compliance monitoring. Features temperature monitoring via TempStick sensor integration and complete audit trail capabilities.

### Technology Stack
- **Frontend**: React 18 + TypeScript, Vite, Tailwind CSS + Radix UI, React Hook Form + Zod
- **Backend**: Supabase (PostgreSQL), Row Level Security, Real-time subscriptions
- **Voice Processing**: OpenAI Whisper API with confidence scoring
- **Sensor Integration**: TempStick API with real-time synchronization
- **Infrastructure**: Supabase Edge Functions, Global CDN, Automated CI/CD
- **Testing**: <PERSON><PERSON><PERSON>, Playwright, Testing Library

### Architecture Pattern
**Service-Oriented Frontend Architecture** with real-time data synchronization:
- Component-based UI with feature-organized structure
- Service layer for business logic and data access
- Real-time subscription system for live updates
- CORS proxy for external API integration
- Voice processing pipeline with quality assurance

### Key Technical Decisions
- Supabase for unified backend (database, auth, storage, realtime)
- TypeScript throughout for type safety
- Voice-first interface with confidence scoring
- Real-time temperature monitoring with automated sync
- HACCP compliance-driven data models

---

## Layer 2: Module Map

### Core Business Modules
- **`src/components/`**: Feature-organized React components (sensors, inventory, voice, compliance)
- **`src/services/`**: Business logic and data access services (inventory, voice, tempstick, compliance)
- **`src/lib/`**: Core libraries (database client, manual sync service, auth helpers)
- **`src/hooks/`**: Custom React hooks for data fetching and state management

### Data Layer
- **`supabase/`**: Database migrations, schema definitions, RLS policies
- **`src/types/`**: TypeScript definitions for database, API responses, business entities
- **Database Schema**: Users, inventory, sensors, temperature_readings, voice_events, compliance_data

### Utility & Infrastructure
- **`src/utils/`**: Helper functions, formatters, validators
- **`scripts/`**: Database operations, sensor sync, development utilities
- **`infrastructure/`**: Deployment configurations, monitoring setup
- **`e2e/`**: End-to-end test scenarios

### External Integrations
- **TempStick API**: Temperature sensor data ingestion via CORS proxy
- **OpenAI API**: Voice transcription and processing
- **Supabase APIs**: Database, auth, storage, and realtime

---

## Layer 3: Integration Guide

### API Endpoints & Routes
- **Frontend Routes**: React Router for SPA navigation (`/inventory`, `/temperature`, `/compliance`, `/voice`)
- **TempStick Integration**: `cors-proxy-server.js` handles API authentication and CORS
- **Supabase Edge Functions**: Background processing and scheduled sync operations

### Database Integration
```sql
-- Key tables and relationships
users (auth integration)
├── sensors (tempstick integration)
│   └── temperature_readings (real-time monitoring)
├── inventory (product management)
│   └── batch_tracking (traceability)
├── voice_events (audio processing)
└── compliance_data (HACCP monitoring)
```

### Configuration Files
- **`vite.config.ts`**: Build configuration, plugins, proxy settings
- **`supabase/config.toml`**: Database and edge function settings
- **`.env`**: Environment variables for API keys, database URLs, feature flags
- **`tailwind.config.js`**: UI styling system configuration

### Integration Points
- **Real-time Subscriptions**: Supabase realtime for live dashboard updates
- **Voice Pipeline**: Speech-to-text → confidence scoring → manual review → database
- **Temperature Sync**: TempStick API → validation → database → dashboard notifications
- **Compliance Reporting**: Data aggregation → PDF generation → audit trail

---

## Layer 4: Extension Points

### Design Patterns
- **Service Layer Pattern**: Business logic separated from UI components
- **Repository Pattern**: Data access abstraction with Supabase client
- **Observer Pattern**: Real-time subscriptions for live data updates
- **Strategy Pattern**: Multiple voice confidence scoring algorithms
- **Factory Pattern**: Component generation for different inventory types

### Customization Areas
- **Voice Commands**: Extend `src/services/voice-service.ts` for new command patterns
- **Sensor Types**: Add new sensor integrations via `src/lib/manual-sync-service.ts`
- **Compliance Rules**: Modify HACCP monitoring in `src/services/compliance-service.ts`
- **Report Formats**: Extend PDF generation in `src/utils/report-generators.ts`

### Plugin Architecture
- **Component Registration**: New inventory types can be registered in component index files
- **Service Extension**: New external APIs can follow the TempStick integration pattern
- **Dashboard Widgets**: Temperature dashboard supports modular sensor display components
- **Voice Processing**: Confidence scoring algorithms can be swapped via configuration

### Development Extension Points
- **Testing Framework**: Vitest + Playwright setup supports new test categories
- **Build Pipeline**: Vite plugin system allows build-time customizations  
- **Database Migrations**: Supabase migration system for schema evolution
- **Performance Monitoring**: Bundle analysis and performance budget enforcement

### Configuration-Driven Features

- **Feature Flags**: Environment-based feature toggles
- **API Rate Limiting**: Configurable retry strategies and backoff algorithms
- **Real-time Sync Intervals**: Adjustable polling frequencies for different data types

---

*Last updated: Auto-generated byterover handbook*