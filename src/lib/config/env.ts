import { Zod<PERSON>rror, z } from 'zod';

const booleanFromEnv = () =>
  z
    .preprocess((value) => {
      if (value === undefined || value === null || value === '') {
        return undefined;
      }

      if (typeof value === 'boolean') {
        return value;
      }

      if (typeof value === 'number') {
        return value !== 0;
      }

      if (typeof value === 'string') {
        const normalized = value.trim().toLowerCase();

        if (['true', '1', 'yes', 'y', 'on'].includes(normalized)) {
          return true;
        }

        if (['false', '0', 'no', 'n', 'off'].includes(normalized)) {
          return false;
        }

        return value;
      }

      return value;
    }, z.boolean())
    .optional();

const numberFromEnv = () =>
  z
    .preprocess((value) => {
      if (value === undefined || value === null || value === '') {
        return undefined;
      }

      if (typeof value === 'number') {
        return value;
      }

      if (typeof value === 'string') {
        const normalized = value.trim();
        if (normalized === '') {
          return undefined;
        }

        return Number(normalized);
      }

      return value;
    }, z.number())
    .optional();

const envSchema = z
  .object({
    MODE: z.string().optional(),
    BASE_URL: z.string().optional(),
    DEV: booleanFromEnv(),
    PROD: booleanFromEnv(),
    SSR: booleanFromEnv(),
    VITE_ENVIRONMENT: z.enum(['development', 'staging', 'production']).optional(),
    VITEST: booleanFromEnv(),
    VITE_SUPABASE_URL: z.string().url({ message: 'VITE_SUPABASE_URL must be a valid URL' }),
    VITE_SUPABASE_ANON_KEY: z
      .string()
      .min(1, { message: 'VITE_SUPABASE_ANON_KEY is required' }),
    VITE_SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),
    SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),
    OPENAI_API_KEY: z.string().min(1).optional(),
    VITE_OPENAI_API_KEY: z.string().min(1).optional(),
    VITE_API_BASE_URL: z.string().url().optional(),
    VITE_TEMPSTICK_API_URL: z.string().url().optional(),
    VITE_TEMPSTICK_BASE_URL: z.string().url().optional(),
    VITE_TEMPSTICK_API_KEY: z
      .string()
      .min(1, { message: 'VITE_TEMPSTICK_API_KEY is required' }),
    VITE_TEMPSTICK_WEBHOOK_URL: z.string().url().optional(),
    VITE_TEMPSTICK_SYNC_INTERVAL: numberFromEnv(),
    VITE_TEMPSTICK_RATE_LIMIT: numberFromEnv(),
    VITE_TEMPSTICK_MAX_RETRIES: numberFromEnv(),
    TEMPSTICK_SYNC_USER_ID: z.string().uuid().optional(),
    TEMPSTICK_ALLOWED_ORIGINS: z.string().optional(),
    ALLOWED_ORIGINS: z.string().optional(),
    VITE_FEATURE_DIRECT_REALTIME: booleanFromEnv(),
    VITE_ENABLE_DIRECT_REALTIME: booleanFromEnv(),
    VITE_FEATURE_USE_WEBRTC: booleanFromEnv(),
    VITE_USE_WEBRTC: booleanFromEnv(),
    VITE_FEATURE_OVERLAY_CLEANUP: booleanFromEnv(),
    VITE_ENABLE_OVERLAY_CLEANUP: booleanFromEnv(),
    VITE_FEATURE_OVERLAY_DIAGNOSTIC: booleanFromEnv(),
    VITE_ENABLE_OVERLAY_DIAGNOSTIC: booleanFromEnv(),
    VITE_SHOW_OVERLAY_DIAGNOSTIC: booleanFromEnv(),
    VITE_FEATURE_BOTTOM_BAR_DEBUG: booleanFromEnv(),
    VITE_SHOW_BOTTOM_BAR_DEBUG: booleanFromEnv(),
    VITE_SKIP_AUTH: booleanFromEnv(),
    VITE_REALTIME_MODEL: z.string().optional(),
    REALTIME_MODEL: z.string().optional(),
    VITE_REALTIME_VOICE: z.string().optional(),
    REALTIME_VOICE: z.string().optional(),
    VITE_DATADOG_API_KEY: z.string().optional(),
    VITE_DATADOG_APP_KEY: z.string().optional(),
    VITE_SENTRY_DSN: z.string().url().optional(),
    VITE_SMTP_HOST: z.string().optional(),
    VITE_SMTP_PORT: numberFromEnv(),
    VITE_SMTP_USERNAME: z.string().optional(),
    VITE_SMTP_PASSWORD: z.string().optional(),
    VITE_SMTP_FROM: z.string().email().optional(),
    VITE_SMS_PROVIDER: z.enum(['twilio', 'aws-sns']).optional(),
    VITE_TWILIO_ACCOUNT_SID: z.string().optional(),
    VITE_TWILIO_AUTH_TOKEN: z.string().optional(),
    VITE_TWILIO_FROM_NUMBER: z.string().optional(),
    VITE_SLACK_WEBHOOK_URL: z.string().url().optional(),
    VITE_SLACK_BOT_TOKEN: z.string().optional(),
  })
  .passthrough()
  .superRefine((values, ctx) => {
    const inferredEnvironment = values.VITE_ENVIRONMENT
      ?? (values.PROD ? 'production' : values.DEV ? 'development' : undefined);

    if (inferredEnvironment === 'production') {
      if (!values.VITE_SENTRY_DSN) {
        ctx.addIssue({
          path: ['VITE_SENTRY_DSN'],
          code: z.ZodIssueCode.custom,
          message: 'VITE_SENTRY_DSN is required in production environments',
        });
      }

      if (!values.VITE_SMTP_HOST) {
        ctx.addIssue({
          path: ['VITE_SMTP_HOST'],
          code: z.ZodIssueCode.custom,
          message: 'VITE_SMTP_HOST is required in production environments',
        });
      }
    }
  });

type RawEnv = z.infer<typeof envSchema>;

let rawEnv: RawEnv;

try {
  rawEnv = envSchema.parse(import.meta.env);
} catch (error) {
  if (error instanceof ZodError) {
    console.error('Environment validation failed:');
    for (const issue of error.issues) {
      console.error(`  • ${issue.path.join('.') || 'root'}: ${issue.message}`);
    }
    throw new Error('Invalid environment configuration. Check logged validation errors.');
  }

  throw error;
}

const resolveFlag = (
  primary: boolean | undefined,
  fallback: boolean | undefined,
  defaultValue: boolean
) => {
  if (typeof primary === 'boolean') {
    return primary;
  }

  if (typeof fallback === 'boolean') {
    return fallback;
  }

  return defaultValue;
};

const parseCsv = (value?: string): string[] =>
  value
    ?.split(',')
    .map((item) => item.trim())
    .filter((item) => item.length > 0)
  ?? [];

const environmentName = rawEnv.VITE_ENVIRONMENT
  ?? (rawEnv.PROD ? 'production' : rawEnv.DEV ? 'development' : 'development');

const isDevelopment = environmentName === 'development';
const isProduction = environmentName === 'production';
const isStaging = environmentName === 'staging';

export interface AppEnv {
  mode: 'development' | 'staging' | 'production';
  baseUrl: string;
  isDevelopment: boolean;
  isProduction: boolean;
  isStaging: boolean;
  isVitest: boolean;
  supabase: {
    url: string;
    anonKey: string;
    serviceRoleKey?: string;
  };
  api: {
    baseUrl?: string;
  };
  tempstick: {
    apiUrl: string;
    apiKey: string;
    webhookUrl?: string;
    syncIntervalMs: number;
    rateLimitPerMinute?: number;
    maxRetries?: number;
    syncUserId?: string;
    configuredApiUrl?: string;
  };
  monitoring: {
    datadogApiKey?: string;
    datadogAppKey?: string;
    sentryDsn?: string;
  };
  notifications: {
    smtp: {
      host?: string;
      port: number;
      username?: string;
      password?: string;
      from: string;
    };
    sms: {
      provider: 'twilio' | 'aws-sns';
      accountSid?: string;
      authToken?: string;
      fromNumber?: string;
    };
    slack: {
      webhookUrl?: string;
      botToken?: string;
    };
  };
  realtime: {
    model: string;
    voice: string;
  };
  featureFlags: {
    directRealtime: boolean;
    useWebRtc: boolean;
    overlayCleanup: boolean;
    overlayDiagnostic: boolean;
    bottomBarDebug: boolean;
    skipAuth: boolean;
  };
  openAi: {
    apiKey?: string;
  };
}

export const appEnv: AppEnv = Object.freeze({
  mode: environmentName,
  baseUrl: rawEnv.BASE_URL ?? '/',
  isDevelopment,
  isProduction,
  isStaging,
  isVitest: resolveFlag(rawEnv.VITEST, undefined, false),
  supabase: {
    url: rawEnv.VITE_SUPABASE_URL.trim(),
    anonKey: rawEnv.VITE_SUPABASE_ANON_KEY.trim(),
    serviceRoleKey:
      rawEnv.SUPABASE_SERVICE_ROLE_KEY?.trim()
      ?? rawEnv.VITE_SUPABASE_SERVICE_ROLE_KEY?.trim(),
  },
  api: {
    baseUrl: rawEnv.VITE_API_BASE_URL?.trim(),
  },
  tempstick: {
    configuredApiUrl:
      rawEnv.VITE_TEMPSTICK_BASE_URL?.trim() ?? rawEnv.VITE_TEMPSTICK_API_URL?.trim(),
    apiUrl:
      rawEnv.VITE_TEMPSTICK_BASE_URL?.trim()
      ?? rawEnv.VITE_TEMPSTICK_API_URL?.trim()
      ?? 'https://tempstickapi.com/api/v1',
    apiKey: rawEnv.VITE_TEMPSTICK_API_KEY.trim(),
    webhookUrl: rawEnv.VITE_TEMPSTICK_WEBHOOK_URL?.trim(),
    syncIntervalMs: rawEnv.VITE_TEMPSTICK_SYNC_INTERVAL ?? 300_000,
    rateLimitPerMinute: rawEnv.VITE_TEMPSTICK_RATE_LIMIT,
    maxRetries: rawEnv.VITE_TEMPSTICK_MAX_RETRIES,
    syncUserId: rawEnv.TEMPSTICK_SYNC_USER_ID,
  },
  monitoring: {
    datadogApiKey: rawEnv.VITE_DATADOG_API_KEY?.trim(),
    datadogAppKey: rawEnv.VITE_DATADOG_APP_KEY?.trim(),
    sentryDsn: rawEnv.VITE_SENTRY_DSN?.trim(),
  },
  notifications: {
    smtp: {
      host: rawEnv.VITE_SMTP_HOST?.trim(),
      port: rawEnv.VITE_SMTP_PORT ?? 587,
      username: rawEnv.VITE_SMTP_USERNAME?.trim(),
      password: rawEnv.VITE_SMTP_PASSWORD?.trim(),
      from: (rawEnv.VITE_SMTP_FROM ?? '<EMAIL>').trim(),
    },
    sms: {
      provider: rawEnv.VITE_SMS_PROVIDER ?? 'twilio',
      accountSid: rawEnv.VITE_TWILIO_ACCOUNT_SID?.trim(),
      authToken: rawEnv.VITE_TWILIO_AUTH_TOKEN?.trim(),
      fromNumber: rawEnv.VITE_TWILIO_FROM_NUMBER?.trim(),
    },
    slack: {
      webhookUrl: rawEnv.VITE_SLACK_WEBHOOK_URL?.trim(),
      botToken: rawEnv.VITE_SLACK_BOT_TOKEN?.trim(),
    },
  },
  realtime: {
    model: (rawEnv.VITE_REALTIME_MODEL ?? rawEnv.REALTIME_MODEL ?? 'gpt-4o-realtime-preview-2024-12-17').trim(),
    voice: (rawEnv.VITE_REALTIME_VOICE ?? rawEnv.REALTIME_VOICE ?? 'alloy').trim(),
  },
  featureFlags: {
    directRealtime: resolveFlag(rawEnv.VITE_FEATURE_DIRECT_REALTIME, rawEnv.VITE_ENABLE_DIRECT_REALTIME, false),
    useWebRtc: resolveFlag(rawEnv.VITE_FEATURE_USE_WEBRTC, rawEnv.VITE_USE_WEBRTC, true),
    overlayCleanup: resolveFlag(rawEnv.VITE_FEATURE_OVERLAY_CLEANUP, rawEnv.VITE_ENABLE_OVERLAY_CLEANUP, false),
    overlayDiagnostic: resolveFlag(
      rawEnv.VITE_FEATURE_OVERLAY_DIAGNOSTIC,
      rawEnv.VITE_ENABLE_OVERLAY_DIAGNOSTIC ?? rawEnv.VITE_SHOW_OVERLAY_DIAGNOSTIC,
      false
    ),
    bottomBarDebug: resolveFlag(
      rawEnv.VITE_FEATURE_BOTTOM_BAR_DEBUG,
      rawEnv.VITE_SHOW_BOTTOM_BAR_DEBUG,
      false
    ),
    skipAuth: resolveFlag(rawEnv.VITE_SKIP_AUTH, undefined, false),
  },
  openAi: {
    apiKey: rawEnv.OPENAI_API_KEY?.trim() ?? rawEnv.VITE_OPENAI_API_KEY?.trim(),
  },
} satisfies AppEnv);

export type RawAppEnv = RawEnv;
