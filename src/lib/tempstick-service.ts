/**
 * Enhanced TempStick API Service
 *
 * Handles integration with TempStick temperature monitoring sensors with:
 * - Robust error handling and retry logic
 * - API rate limiting and request throttling
 * - Comprehensive logging and monitoring
 * - Data quality validation
 * - Health check endpoints
 *
 * @see https://tempstickapi.com/docs/
 */

import { supabase } from './supabase';
import { appEnv } from '@/lib/config/env';
import type {
  TempStickSensor,
  TempStickReading,
  TempStickApiResponse,
  TemperatureReading,
  SyncResponse,
  SystemHealth,
} from '../types/tempstick';
// Note: historical dashboard types are not used in this service

// Enhanced error types for better error handling
export class TempStickApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public retryable: boolean = false,
    public rateLimited: boolean = false
  ) {
    super(message);
    this.name = 'TempStickApiError';
  }
}

export class TempStickServiceError extends Error {
  constructor(
    message: string,
    public operation: string,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = 'TempStickServiceError';
  }
}

// Rate limiting and retry configuration
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

interface RateLimitConfig {
  requestsPerMinute: number;
  burstLimit: number;
}

// Service health monitoring
interface ServiceMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  rateLimitedRequests: number;
  averageResponseTime: number;
  lastSyncTime: Date | null;
  lastErrorTime: Date | null;
  lastError: string | null;
}

// Minimal API sensor shape used by this service
interface ApiSensor {
  id?: string;
  sensor_id?: string;
  sensor_name?: string;
  name?: string;
  last_temp?: string | number;
  last_checkin?: string;
  last_humidity?: string | number;
  battery_pct?: string | number;
  rssi?: string | number;
  offline?: string | boolean;
  latest_reading?: {
    timestamp?: string;
    battery_pct?: string | number;
    signal?: string | number;
    rssi?: string | number;
  };
  battery_level?: string | number;
  signal_strength?: string | number;
  latest?: {
    timestamp?: string;
    battery_pct?: string | number;
    signal?: string | number;
    rssi?: string | number;
  };
  [key: string]: unknown;
}

type SensorsApiResponse = {
  data?: { items?: ApiSensor[]; sensors?: ApiSensor[] };
  sensors?: ApiSensor[];
};

/**
 * Enhanced TempStick API Client with rate limiting and retry logic
 */
class TempStickApiClient {
  private apiKey: string;
  private baseUrl: string;
  private retryConfig: RetryConfig;
  private rateLimitConfig: RateLimitConfig;
  private requestQueue: Array<{
    timestamp: number;
    resolve: () => void;
    reject: (reason?: unknown) => void;
  }> = [];
  private metrics: ServiceMetrics;
  // Coalescing + caching for sensors endpoint to reduce upstream pressure
  private sensorsPromise: Promise<unknown> | null = null;
  private sensorsCache: { at: number; data: unknown } | null = null;
  private sensorsCacheTTL = 60_000; // 60 seconds

  constructor(
    apiKey: string,
    config?: {
      retry?: Partial<RetryConfig>;
      rateLimit?: Partial<RateLimitConfig>;
      baseUrl?: string;
    }
  ) {
    if (!apiKey) {
      throw new TempStickApiError('TempStick API key is required');
    }

    this.apiKey = apiKey;
    // Always use a relative proxy path in the browser to avoid exposing secrets
    // - DEV: Vite dev server proxies `/api/tempstick/*` to the upstream API
    // - PREVIEW/PROD: Vercel serverless function at `/api/tempstick/*` proxies upstream
    // - Node scripts: use local relay server during dev, else upstream
    const isBrowser = typeof window !== 'undefined';
    this.baseUrl = config?.baseUrl
      ?? (isBrowser
        ? '/api/tempstick'
        : (appEnv.isDevelopment
            ? '/api/tempstick'  // Use Vite proxy in all environments
            : (process.env.TEMPSTICK_API_URL ?? 'https://tempstickapi.com/api/v1')));
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      ...config?.retry,
    };
    this.rateLimitConfig = {
      requestsPerMinute: 60,
      burstLimit: 10,
      ...config?.rateLimit,
    };
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      rateLimitedRequests: 0,
      averageResponseTime: 0,
      lastSyncTime: null,
      lastErrorTime: null,
      lastError: null,
    };
  }

  /**
   * Enhanced request method with retry logic and rate limiting
   */
  private async request<T>(
    endpoint: string,
    options?: RequestInit
  ): Promise<TempStickApiResponse<T>> {
    const startTime = Date.now();
    let lastError: Error;

    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      let timeoutId: ReturnType<typeof setTimeout> | null = null;
      try {
        // Apply rate limiting
        await this.enforceRateLimit();

        // Fix: Ensure proper endpoint construction
        const url = `${this.baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
        this.metrics.totalRequests++;

        console.log(`🌐 TempStick API Request: ${url}`);

        const isBrowserEnv = typeof window !== 'undefined';
        const controller = new AbortController();
        timeoutId = setTimeout(() => controller.abort(), 30000);

        const response = await fetch(url, {
          ...options,
          headers: {
            // Never send API key from the browser; proxies add it server-side
            ...(isBrowserEnv ? {} : { 'X-API-KEY': this.apiKey }),
            'Content-Type': 'application/json',
            // User-Agent is ignored by browsers; dev proxy sets UA appropriately
            'User-Agent': 'Seafood-Manager/1.0',
            Accept: 'application/json',
            ...options?.headers,
          },
          signal: controller.signal,
        });

        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        const responseTime = Date.now() - startTime;
        this.updateMetrics(responseTime, true);

        console.log(`📡 TempStick API Response: ${response.status} ${response.statusText}`);

        if (!response.ok) {
          const errorText = await response.text();
          const isRateLimited = response.status === 429;
          const isRetryable = response.status >= 500 || isRateLimited;

          if (isRateLimited) {
            this.metrics.rateLimitedRequests++;
            const retryAfter = response.headers.get('Retry-After');
            const delay = retryAfter ? parseInt(retryAfter) * 1000 : this.calculateDelay(attempt);

            if (attempt < this.retryConfig.maxRetries) {
              console.warn(
                `⏳ Rate limited, retrying after ${delay}ms (attempt ${attempt + 1}/${this.retryConfig.maxRetries + 1})`
              );
              await this.sleep(delay);
              continue;
            }
          }

          throw new TempStickApiError(
            `TempStick API error: ${response.status} ${response.statusText} - ${errorText}`,
            response.status,
            isRetryable,
            isRateLimited
          );
        }

        const data = await response.json();
        console.log(`✅ TempStick API Success:`, data);

        this.metrics.successfulRequests++;
        this.metrics.lastSyncTime = new Date();

        return data;
      } catch (error) {
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        const elapsed = Date.now() - startTime;
        let normalizedError: Error;

        if (error instanceof Error) {
          normalizedError = error;
        } else {
          normalizedError = new Error(String(error));
        }

        const isAbortError =
          (typeof DOMException !== 'undefined'
            && error instanceof DOMException
            && error.name === 'AbortError')
          || normalizedError.name === 'AbortError';

        if (isAbortError) {
          normalizedError = new TempStickApiError(
            'TempStick request timed out after 30000ms',
            undefined,
            true
          );
        }

        lastError = normalizedError;
        this.updateMetrics(elapsed, false, normalizedError);

        if (normalizedError instanceof TempStickApiError && !normalizedError.retryable) {
          throw normalizedError;
        }

        if (attempt < this.retryConfig.maxRetries) {
          const delay = this.calculateDelay(attempt);
          console.warn(
            `⚠️ Request failed, retrying after ${delay}ms (attempt ${attempt + 1}/${this.retryConfig.maxRetries + 1}):`,
            normalizedError.message
          );
          await this.sleep(delay);
        }
      }
    }

    throw new TempStickApiError(
      `Request failed after ${this.retryConfig.maxRetries + 1} attempts: ${lastError.message}`,
      undefined,
      false
    );
  }

  /**
   * Rate limiting enforcement
   */
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // Clean old requests from queue
    this.requestQueue = this.requestQueue.filter((req) => req.timestamp > oneMinuteAgo);

    // Check if we're within rate limits
    if (this.requestQueue.length >= this.rateLimitConfig.requestsPerMinute) {
      const oldestRequest = this.requestQueue[0];
      const waitTime = 60000 - (now - oldestRequest.timestamp);

      if (waitTime > 0) {
        console.log(`⏱️ Rate limit reached, waiting ${waitTime}ms`);
        await this.sleep(waitTime);
      }
    }

    // Add current request to queue
    this.requestQueue.push({
      timestamp: now,
      resolve: () => {},
      reject: () => {},
    });
  }

  /**
   * Calculate exponential backoff delay
   */
  private calculateDelay(attempt: number): number {
    const delay =
      this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt);
    return Math.min(delay, this.retryConfig.maxDelay);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Update service metrics
   */
  private updateMetrics(responseTime: number, success: boolean, error?: Error): void {
    if (success) {
      this.metrics.averageResponseTime =
        (this.metrics.averageResponseTime * this.metrics.successfulRequests + responseTime) /
        (this.metrics.successfulRequests + 1);
    } else {
      this.metrics.failedRequests++;
      this.metrics.lastErrorTime = new Date();
      this.metrics.lastError = error?.message ?? 'Unknown error';
    }
  }

  /**
   * Get service health metrics
   */
  public getMetrics(): ServiceMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics (useful for monitoring)
   */
  public resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      rateLimitedRequests: 0,
      averageResponseTime: 0,
      lastSyncTime: null,
      lastErrorTime: null,
      lastError: null,
    };
  }

  /**
   * Get all sensors from TempStick API v1 - Fixed endpoint
   */
  async getSensors(): Promise<ApiSensor[]> {
    try {
      console.log('🔍 Fetching sensors from /sensors/all endpoint...');
      // Short-circuit from cache if fresh
      if (this.sensorsCache && Date.now() - this.sensorsCache.at < this.sensorsCacheTTL) {
        const cached = this.sensorsCache.data;
        const cachedSensors = cached?.data?.items ?? cached?.sensors ?? cached?.data?.sensors ?? [];
        console.log(`🗄️ Using cached sensors: ${cachedSensors?.length ?? 0}`);
        return Array.isArray(cachedSensors)
          ? cachedSensors.filter((sensor: ApiSensor) => this.validateSensorData(sensor))
          : [];
      }

      // Coalesce concurrent requests
      this.sensorsPromise ??= this.request<SensorsApiResponse>('/sensors/all')
          .then((resp) => {
            this.sensorsCache = { at: Date.now(), data: resp };
            return resp;
          })
          .catch((err) => {
            if (err instanceof TempStickApiError && err.statusCode === 404) {
              const guidance =
                'TempStick 404 for /sensors/all. Likely missing/invalid X-API-KEY or blocked User-Agent. ' +
                'Ensure requests go through the Vite proxy (/api/tempstick/...) and VITE_TEMPSTICK_API_KEY is set. ' +
                'Avoid calling the upstream host directly from the browser.';
              throw new TempStickApiError(guidance, 404, false, false);
            }
            throw err;
          })
          .finally(() => {
            // Release the coalesced promise shortly after resolution
            setTimeout(() => {
              this.sensorsPromise = null;
            }, 50);
          });

      const response = await this.sensorsPromise;

      // Handle TempStick API response format according to actual API response
      // The API returns: { type: "success", data: { items: [...] } }
      const sensors = response?.data?.items ?? response?.sensors ?? response?.data?.sensors ?? [];

      console.log(`📊 Retrieved ${sensors?.length ?? 0} sensors from API`);

      if (!Array.isArray(sensors)) {
        console.warn('⚠️ Sensors data is not an array:', sensors);
        return [];
      }

      // Validate sensor data quality
      const validSensors = sensors.filter((sensor: TempStickSensor) =>
        this.validateSensorData(sensor)
      );
      console.log(`✅ ${validSensors.length} sensors passed validation`);

      return validSensors;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Failed to fetch sensors:', errorMessage);
      // Resiliency: fallback to cached sensors if available
      if (this.sensorsCache) {
        try {
          const cached = this.sensorsCache.data;
          const cachedSensors = cached?.data?.items ?? cached?.sensors ?? cached?.data?.sensors ?? [];
          if (Array.isArray(cachedSensors)) {
            console.warn('🗄️ Using cached sensors due to upstream error');
            return cachedSensors.filter((sensor: ApiSensor) => this.validateSensorData(sensor));
          }
        } catch {
          // ignore cache parse errors
        }
      }
      throw new TempStickApiError(
        `Failed to fetch sensors: ${errorMessage}`,
        error instanceof TempStickApiError ? error.statusCode : undefined,
        true
      );
    }
  }

  /**
   * Get latest readings for a specific sensor - Using sensor's latest data
   */
  async getLatestReadings(sensorId: string, _limit: number = 100): Promise<TempStickReading[]> {
    try {
      console.log(`🔍 Getting latest reading for sensor ${sensorId} from sensors data...`);

      // Since historical readings endpoint doesn't exist, get sensor data
      // and create a synthetic reading from the latest sensor information
      const sensorsResponse = await this.request<SensorsApiResponse>('/sensors/all');
      const sensors =
        sensorsResponse?.data?.items ??
        sensorsResponse?.sensors ??
        sensorsResponse?.data?.sensors ??
        [];

      const sensor = sensors.find((s) => s.id === sensorId || s.sensor_id === sensorId);

      if (!sensor) {
        console.warn(`⚠️ Sensor ${sensorId} not found`);
        return [];
      }

      // Create a synthetic reading from the sensor's latest data
      if (sensor.last_temp !== undefined && sensor.last_checkin) {
        const reading: TempStickReading = {
          temperature: parseFloat(sensor.last_temp),
          humidity:
            sensor.last_humidity !== undefined ? parseFloat(sensor.last_humidity) : undefined,
          timestamp: sensor.last_checkin,
          sensor_id: sensorId,
          battery_level: sensor.battery_pct,
          signal_strength: sensor.rssi ? parseInt(sensor.rssi) : undefined,
        };

        console.log(`✅ Created synthetic reading for sensor ${sensorId} from latest data`);
        return [reading];
      }

      console.warn(`📭 No recent data available for sensor ${sensorId}`);
      return [];
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Failed to fetch readings for sensor ${sensorId}:`, errorMessage);

      // Don't throw error, just return empty array for graceful degradation
      return [];
    }
  }

  /**
   * Get historical readings for a sensor within a date range - Using latest sensor data
   */
  async getReadingsForPeriod(
    sensorId: string,
    startDate: Date,
    endDate: Date
  ): Promise<TempStickReading[]> {
    try {
      console.log(
        `🔍 Getting historical data for sensor ${sensorId} (using latest sensor data)...`
      );

      // Since historical readings endpoint doesn't exist, return the latest reading
      // if it falls within the requested date range
      const latestReadings = await this.getLatestReadings(sensorId, 1);

      if (latestReadings.length === 0) {
        console.log(`📭 No data available for sensor ${sensorId}`);
        return [];
      }

      const reading = latestReadings[0];
      const readingDate = new Date(reading.timestamp);

      // Check if the latest reading falls within the requested date range
      if (readingDate >= startDate && readingDate <= endDate) {
        console.log(`✅ Latest reading for sensor ${sensorId} is within date range`);
        return latestReadings;
      } else {
        console.log(`📅 Latest reading for sensor ${sensorId} is outside requested date range`);
        return [];
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Failed to fetch historical readings for sensor ${sensorId}:`, errorMessage);
      // Don't throw error, return empty array for graceful degradation
      return [];
    }
  }

  /**
   * Get all alerts from TempStick API - New implementation
   */
  async getAlerts(): Promise<Record<string, unknown>[]> {
    try {
      console.log('🔍 Fetching alerts from /alerts endpoint...');

      type AlertsApiResponse = {
        alerts?: Record<string, unknown>[];
        data?: { alerts?: Record<string, unknown>[]; items?: Record<string, unknown>[] };
      };
      const response = await this.request<AlertsApiResponse>('/alerts');

      // Handle TempStick API response format
      const alerts = response?.alerts ?? response?.data?.alerts ?? response?.data?.items ?? [];

      console.log(`📊 Retrieved ${alerts?.length ?? 0} alerts from API`);

      if (!Array.isArray(alerts)) {
        console.warn('⚠️ Alerts data is not an array:', alerts);
        return [];
      }

      return alerts;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Failed to fetch alerts:', errorMessage);
      throw new TempStickApiError(`Failed to fetch alerts: ${errorMessage}`, undefined, true);
    }
  }

  /**
   * Get user notifications from TempStick API - New implementation
   */
  async getNotifications(): Promise<Record<string, unknown>[]> {
    try {
      console.log('🔍 Fetching notifications from /notifications/user endpoint...');

      type NotificationsApiResponse = {
        notifications?: Record<string, unknown>[];
        data?: { notifications?: Record<string, unknown>[]; items?: Record<string, unknown>[] };
      };
      const response = await this.request<NotificationsApiResponse>('/notifications/user');

      // Handle TempStick API response format
      const notifications =
        response?.notifications ?? response?.data?.notifications ?? response?.data?.items ?? [];

      console.log(`📊 Retrieved ${notifications?.length ?? 0} notifications from API`);

      if (!Array.isArray(notifications)) {
        console.warn('⚠️ Notifications data is not an array:', notifications);
        return [];
      }

      return notifications;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Failed to fetch notifications:', errorMessage);
      throw new TempStickApiError(
        `Failed to fetch notifications: ${errorMessage}`,
        undefined,
        true
      );
    }
  }

  /**
   * Get sensor health status - Enhanced implementation
   */
  async getSensorHealth(
    sensorId: string
  ): Promise<{ online: boolean; batteryLevel: number; signalStrength: number }> {
    try {
      // TempStick API does not expose /sensors/{id}/health; use supported endpoints
      // Prefer single sensor endpoint first
      type SingleSensorApiResponse = {
        sensor?: ApiSensor;
        data?: { sensor?: ApiSensor };
        [key: string]: unknown;
      };
      const response = await this.request<SingleSensorApiResponse>(
        `/sensor/${encodeURIComponent(sensorId)}`
      );
      const sensor = response?.sensor ?? response?.data?.sensor ?? undefined;

      if (sensor) {
        const latest = sensor.latest_reading ?? sensor.latest ?? {};
        const battery = sensor.battery_level ?? sensor.battery_pct ?? latest.battery_pct;
        const signal = sensor.signal_strength ?? sensor.signal ?? latest.signal ?? latest.rssi;
        const ts = latest.timestamp ?? latest.date ?? latest.recorded_at;
        const online = ts ? Date.now() - new Date(ts).getTime() < 15 * 60 * 1000 : false;
        return {
          online,
          batteryLevel: Number.isFinite(parseInt(battery)) ? parseInt(battery) : 0,
          signalStrength: Number.isFinite(parseInt(signal)) ? parseInt(signal) : 0,
        };
      }

      // Fallback: fetch all and find the sensor
      const all = await this.request<SensorsApiResponse>('/sensors/all');
      const allSensors = all?.data?.items ?? all?.sensors ?? all?.data?.sensors ?? [];
      const match = allSensors.find((s) => (s.sensor_id ?? s.id) === sensorId) ?? ({} as ApiSensor);
      const latest = match.latest_reading ?? {};
      return {
        online: latest.timestamp
          ? Date.now() - new Date(latest.timestamp).getTime() < 15 * 60 * 1000
          : false,
        batteryLevel: Number.isFinite(parseInt(String(match.battery_pct)))
          ? parseInt(String(match.battery_pct))
          : 0,
        signalStrength: Number.isFinite(parseInt(String(latest.signal)))
          ? parseInt(String(latest.signal))
          : 0,
      };
    } catch {
      // Quiet fallback to avoid noisy logs in UI
      return { online: false, batteryLevel: 0, signalStrength: 0 };
    }
  }

  /**
   * Batch get multiple sensors' latest readings - Enhanced implementation
   */
  async getBatchLatestReadings(sensorIds: string[]): Promise<Record<string, TempStickReading[]>> {
    const results: Record<string, TempStickReading[]> = {};

    console.log(`🔄 Fetching batch readings for ${sensorIds.length} sensors...`);

    // Process in batches to avoid overwhelming the API
    const batchSize = 5;
    for (let i = 0; i < sensorIds.length; i += batchSize) {
      const batch = sensorIds.slice(i, i + batchSize);
      console.log(
        `📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(sensorIds.length / batchSize)}...`
      );

      const promises = batch.map(async (sensorId) => {
        try {
          const readings = await this.getLatestReadings(sensorId, 10);
          return { sensorId, readings };
        } catch (error) {
          console.warn(`⚠️ Failed to get readings for sensor ${sensorId}:`, error.message);
          return { sensorId, readings: [] };
        }
      });

      const batchResults = await Promise.all(promises);
      batchResults.forEach(({ sensorId, readings }) => {
        results[sensorId] = readings;
      });

      // Small delay between batches to be respectful to the API
      if (i + batchSize < sensorIds.length) {
        await this.sleep(500); // Increased delay for better rate limiting
      }
    }

    console.log(`✅ Batch fetch completed for ${Object.keys(results).length} sensors`);
    return results;
  }

  /**
   * Validate sensor data quality - Enhanced validation
   */
  private validateSensorData(sensor: ApiSensor): boolean {
    if (!sensor) {
      console.warn('⚠️ Invalid sensor: null or undefined');
      return false;
    }

    // Check for required sensor ID fields (TempStick API format)
    const sensorId = sensor.sensor_id ?? sensor.id;
    if (!sensorId) {
      console.warn('⚠️ Invalid sensor: missing sensor_id', sensor);
      return false;
    }

    // Check for sensor name (TempStick API format)
    const sensorName = sensor.sensor_name ?? sensor.name;
    if (!sensorName || typeof sensorName !== 'string') {
      console.warn('⚠️ Invalid sensor: missing sensor_name', sensor);
      return false;
    }

    // Validate temperature if present
    if (
      sensor.last_temp !== undefined &&
      (isNaN(parseFloat(String(sensor.last_temp))) ||
        (parseFloat(String(sensor.last_temp)) < -100 || parseFloat(String(sensor.last_temp)) > 200))
    ) {
      console.warn('⚠️ Invalid sensor: temperature out of reasonable range', sensor);
      return false;
    }

    return true;
  }

  /**
   * Validate temperature reading data quality - Enhanced validation
   */
  private validateReadingData(reading: {
    sensor_id?: string;
    timestamp?: string;
    temperature?: number;
    humidity?: number;
    [key: string]: unknown;
  }): boolean {
    if (!reading) {
      console.warn('⚠️ Invalid reading: null or undefined');
      return false;
    }

    // Check required fields
    if (!reading.sensor_id || !reading.timestamp) {
      console.warn('⚠️ Invalid reading: missing sensor_id or timestamp', reading);
      return false;
    }

    // Validate temperature range (reasonable for food storage)
    if (
      typeof reading.temperature !== 'number' ||
      isNaN(reading.temperature) || (reading.temperature < -50 || reading.temperature > 150)
    ) {
      console.warn('⚠️ Invalid reading: temperature out of reasonable range', reading);
      return false;
    }

    // Validate humidity if present
    if (
      reading.humidity !== undefined &&
      (typeof reading.humidity !== 'number' ||
        isNaN(reading.humidity) || reading.humidity < 0 || reading.humidity > 100)
    ) {
      console.warn('⚠️ Invalid reading: humidity out of valid range', reading);
      return false;
    }

    // Validate timestamp
    const readingTime = new Date(reading.timestamp as string);
    const now = new Date();
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

    if (isNaN(readingTime.getTime()) || (readingTime > now || readingTime < oneYearAgo)) {
      console.warn('⚠️ Invalid reading: timestamp out of reasonable range', reading);
      return false;
    }

    return true;
  }

  /**
   * Test API connectivity and authentication - Enhanced testing
   */
  async testConnection(): Promise<{ success: boolean; latency: number; error?: string }> {
    const startTime = Date.now();

    try {
      console.log('🔍 Testing TempStick API connection...');

      await this.request('/sensors/all');
      const latency = Date.now() - startTime;

      console.log(`✅ Connection test successful - latency: ${latency}ms`);
      return { success: true, latency };
    } catch (error) {
      const latency = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown connection error';

      console.error(`❌ Connection test failed - latency: ${latency}ms, error:`, errorMessage);
      return {
        success: false,
        latency,
        error: errorMessage,
      };
    }
  }
}

/**
 * Enhanced TempStick Service for Seafood Manager Integration
 */
export class TempStickService {
  private apiClient: TempStickApiClient | null;
  private syncInProgress: boolean = false;
  private lastFullSync: Date | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private lastApiCall: number = 0;
  private minCallInterval: number = 2000; // Minimum 2 seconds between API calls

  constructor(config?: {
    apiKey?: string;
    retry?: Partial<RetryConfig>;
    rateLimit?: Partial<RateLimitConfig>;
    enableHealthChecks?: boolean;
  }) {
    const apiKey = config?.apiKey ?? appEnv.tempstick.apiKey;

    // Only create API client if we have an API key
    if (apiKey) {
      try {
        this.apiClient = new TempStickApiClient(apiKey, {
          retry: config?.retry,
          rateLimit: config?.rateLimit,
        });
      } catch (error) {
        console.error('Failed to initialize TempStick API client:', error.message);
        throw new TempStickServiceError(
          'Failed to initialize TempStick API client',
          'initialization'
        );
      }
    } else {
      console.error(
        '🔧 No TempStick API key found. Please configure VITE_TEMPSTICK_API_KEY environment variable.'
      );
      throw new TempStickServiceError('TempStick API key is required', 'initialization');
    }

    // Start health checks if enabled
    if (config?.enableHealthChecks !== false) {
      this.startHealthChecks();
    }
  }

  /**
   * Start periodic health checks
   */
  private startHealthChecks(): void {
    // Check every 5 minutes
    this.healthCheckInterval = setInterval(
      async () => {
        try {
          await this.performHealthCheck();
        } catch (error) {
          console.error('Health check failed:', error);
        }
      },
      5 * 60 * 1000
    );
  }

  /**
   * Stop health checks
   */
  public stopHealthChecks(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * Perform comprehensive health check
   */
  public async performHealthCheck(): Promise<SystemHealth> {
    const startTime = Date.now();

    try {
      // Test API connectivity
      const apiTest = await this.apiClient.testConnection();

      // Get database health
      const dbHealth = await this.checkDatabaseHealth();

      // Get sensor status summary
      const sensorSummary = await this.getSensorSummary();

      // Get alert summary
      const alertSummary = await this.getAlertSummary();

      return {
        tempstickApi: {
          status: apiTest.success ? 'healthy' : 'down',
          latency: apiTest.latency,
          lastCheck: new Date().toISOString(),
        },
        database: dbHealth,
        sensors: sensorSummary,
        alerts: alertSummary,
      };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        tempstickApi: {
          status: 'down',
          latency: Date.now() - startTime,
          lastCheck: new Date().toISOString(),
        },
        database: {
          status: 'down',
          activeConnections: 0,
          lastMigration: 'unknown',
        },
        sensors: {
          total: 0,
          online: 0,
          offline: 0,
          lowBattery: 0,
        },
        alerts: {
          active: 0,
          unresolved: 0,
          critical: 0,
        },
      };
    }
  }

  /**
   * Check database connectivity and health
   */
  private async checkDatabaseHealth(): Promise<SystemHealth['database']> {
    try {
      const { error } = await supabase.from('sensors').select('id').limit(1);

      if (error) {
        return {
          status: 'down',
          activeConnections: 0,
          lastMigration: 'unknown',
        };
      }

      return {
        status: 'healthy',
        activeConnections: 1, // Supabase handles connection pooling
        lastMigration: new Date().toISOString(),
      };
    } catch {
      return {
        status: 'down',
        activeConnections: 0,
        lastMigration: 'unknown',
      };
    }
  }

  /**
   * Get sensor status summary
   */
  private async getSensorSummary(): Promise<SystemHealth['sensors']> {
    try {
      const { data: sensors, error } = await supabase
        .from('sensors')
        .select('is_online, battery_level')
        .eq('is_active', true);

      if (error ?? !sensors) {
        return { total: 0, online: 0, offline: 0, lowBattery: 0 };
      }

      const total = sensors.length;
      const online = sensors.filter((s) => s.is_online).length;
      const offline = total - online;
      const lowBattery = sensors.filter((s) => s.battery_level && s.battery_level < 20).length;

      return { total, online, offline, lowBattery };
    } catch (error) {
      console.error('Failed to get sensor summary:', error);
      return { total: 0, online: 0, offline: 0, lowBattery: 0 };
    }
  }

  /**
   * Get alert status summary
   */
  private async getAlertSummary(): Promise<SystemHealth['alerts']> {
    try {
      const { data: alerts, error } = await supabase
        .from('temperature_alerts')
        .select('alert_status, severity')
        .eq('alert_status', 'active');

      if (error ?? !alerts) {
        return { active: 0, unresolved: 0, critical: 0 };
      }

      const active = alerts.length;
      const unresolved = alerts.filter((a) => a.alert_status !== 'resolved').length;
      const critical = alerts.filter((a) => a.severity === 'critical').length;

      return { active, unresolved, critical };
    } catch (error) {
      console.error('Failed to get alert summary:', error);
      return { active: 0, unresolved: 0, critical: 0 };
    }
  }

  /**
   * Sync sensors from TempStick API to database
   */
  public async syncSensors(): Promise<SyncResponse> {
    if (this.syncInProgress) {
      console.warn('Sync already in progress, skipping...');
      return {
        success: false,
        syncedSensors: 0,
        newReadings: 0,
        newAlerts: 0,
        errors: ['Sync already in progress'],
        lastSyncTime: this.lastFullSync?.toISOString() ?? 'never',
      };
    }

    this.syncInProgress = true;
    const syncStartTime = new Date();

    try {
      console.log('🔄 Starting TempStick sensor sync...');

      if (!this.apiClient) {
        throw new TempStickServiceError('API client not initialized', 'sync');
      }

      // Get sensors from TempStick API
      const sensors = await this.apiClient.getSensors();
      console.log(`📡 Retrieved ${sensors.length} sensors from TempStick API`);

      let syncedSensors = 0;
      let newReadings = 0;
      const errors: string[] = [];

      // Sync each sensor to database
      for (const sensor of sensors) {
        try {
          // Upsert sensor record
          const { data: upsertedSensor, error: sensorError } = await supabase
            .from('sensors')
            .upsert(
              {
                sensor_id: sensor.sensor_id,
                tempstick_sensor_id: sensor.id,
                name: sensor.sensor_name,
                device_name: sensor.sensor_name,
                location_description: sensor.sensor_name,
                sensor_type: 'temperature_humidity',
                // Treat explicit boolean false or certain string flags as online
                is_online:
                  ((sensor.offline === false) || (sensor.offline === '0') || (sensor.offline === 'false')),
                battery_level: Number.isFinite(Number(sensor.battery_pct))
                  ? Number(sensor.battery_pct)
                  : 0,
                is_active: true,
                updated_at: new Date().toISOString(),
              },
              {
                onConflict: 'sensor_id',
              }
            )
            .select('id, sensor_id')
            .single();

          if (sensorError) {
            console.error(`Failed to sync sensor ${sensor.sensor_id}:`, sensorError.message);
            errors.push(`Sensor ${sensor.sensor_id}: ${sensorError.message}`);
            continue;
          }

          const internalSensorId = upsertedSensor?.id as string | undefined;
          if (!internalSensorId) {
            console.error(`Failed to resolve internal sensor ID for ${sensor.sensor_id}`);
            errors.push(`Sensor ${sensor.sensor_id}: missing internal UUID after upsert`);
            continue;
          }

          // Get latest readings for this sensor (use external TempStick sensor_id)
          const externalSensorId = String(sensor.sensor_id ?? sensor.id ?? '');
          if (!externalSensorId) {
            console.warn(`⚠️ Skipping readings fetch - missing external sensor_id for ${sensor.sensor_name}`);
            continue;
          }
          const readings = await this.apiClient.getLatestReadings(externalSensorId, 10);

          // Insert temperature readings
          for (const reading of readings) {
            const { error: readingError } = await supabase
              .from('temperature_readings')
              .upsert(
                {
                  // IMPORTANT: use internal sensors UUID, not external API id
                  sensor_id: internalSensorId,
                  temp_celsius: reading.temperature,
                  humidity: reading.humidity,
                  recorded_at: reading.timestamp,
                  alert_triggered: false,
                },
                {
                  onConflict: 'sensor_id,recorded_at',
                }
              );

            if (readingError) {
              console.error(
                `Failed to insert reading for sensor ${sensor.sensor_id}:`,
                readingError.message
              );
              errors.push(`Reading for ${sensor.sensor_id}: ${readingError.message}`);
            } else {
              newReadings++;
            }
          }

          syncedSensors++;
        } catch (sensorSyncError) {
          const errorMsg =
            sensorSyncError instanceof Error ? sensorSyncError.message : 'Unknown error';
          console.error(`Failed to sync sensor ${sensor.sensor_id}:`, errorMsg);
          errors.push(`Sensor ${sensor.sensor_id}: ${errorMsg}`);
        }
      }

      this.lastFullSync = syncStartTime;

      const result: SyncResponse = {
        success: errors.length === 0,
        syncedSensors,
        newReadings,
        newAlerts: 0, // Alerts are handled separately
        errors,
        lastSyncTime: syncStartTime.toISOString(),
      };

      console.log(
        `✅ Sync completed: ${syncedSensors} sensors, ${newReadings} readings, ${errors.length} errors`
      );
      return result;
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown sync error';
      console.error('❌ Sync failed:', errorMsg);

      return {
        success: false,
        syncedSensors: 0,
        newReadings: 0,
        newAlerts: 0,
        errors: [errorMsg],
        lastSyncTime: this.lastFullSync?.toISOString() ?? 'never',
      };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Alias for getSensors() method for compatibility with existing components
   */
  public async getAllSensors(): Promise<
    Array<
      {
        sensor_id: string;
        sensor_name: string;
        location: string;
        status: 'online' | 'offline';
        last_temp: number;
        last_humidity: number;
        last_reading: string;
        battery_level: number;
        rssi: number;
      } & Record<string, unknown>
    >
  > {
    if (!this.apiClient) {
      throw new TempStickServiceError('API client not initialized', 'getAllSensors');
    }

    try {
      // Rate limiting check
      const now = Date.now();
      const timeSinceLastCall = now - this.lastApiCall;
      if (timeSinceLastCall < this.minCallInterval) {
        const waitTime = this.minCallInterval - timeSinceLastCall;
        console.log(`⏱️ Rate limiting: waiting ${waitTime}ms before API call`);
        await new Promise((resolve) => setTimeout(resolve, waitTime));
      }
      this.lastApiCall = Date.now();

      // Get sensors from API
      const apiSensors = await this.apiClient.getSensors();

      // Transform to expected format for dashboard components
      type SensorRow = {
        sensor_id: string;
        sensor_name: string;
        location: string;
        status: 'online' | 'offline';
        last_temp: number;
        last_humidity: number;
        last_reading: string;
        battery_level: number;
        rssi: number;
      } & Record<string, unknown>;

      const transformedSensors: SensorRow[] = apiSensors.map((sensor): SensorRow => {
        const extId = sensor.sensor_id ?? sensor.id ?? '';
        const name = sensor.sensor_name ?? sensor.name ?? 'Unknown Sensor';
        const lastTempNum = Number.parseFloat(String(sensor.last_temp ?? 'NaN'));
        const lastHumidityNum = Number.parseFloat(String(sensor.last_humidity ?? 'NaN'));
        const batteryPctNum = Number.parseInt(String(sensor.battery_pct ?? '0'));
        const rssiNum = Number.parseInt(String(sensor.rssi ?? '-50'));
        const lastCheckin = sensor.last_checkin ?? new Date().toISOString();
        const online: 'online' | 'offline' =
          sensor.offline === false || sensor.offline === '0' || sensor.offline === 'false'
            ? 'online'
            : 'offline';

        return {
          ...((sensor as unknown) as Record<string, unknown>),
          sensor_id: String(extId),
          sensor_name: String(name),
          location: String(name),
          status: online,
          last_temp: Number.isFinite(lastTempNum) ? lastTempNum : 0,
          last_humidity: Number.isFinite(lastHumidityNum) ? lastHumidityNum : 0,
          last_reading: String(lastCheckin),
          battery_level: Number.isFinite(batteryPctNum) ? batteryPctNum : 0,
          rssi: Number.isFinite(rssiNum) ? rssiNum : -50,
        };
      });

      console.log(`📊 getAllSensors() returning ${transformedSensors.length} sensors`);
      return transformedSensors;
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ getAllSensors() failed:', errorMsg);
      throw new TempStickServiceError(
        `Failed to get all sensors: ${errorMsg}`,
        'getAllSensors',
        true
      );
    }
  }

  /**
   * Get temperature readings for a date range from the database
   * This method queries the local database, not the TempStick API
   */
  public async getReadingsForDateRange(
    startDate: Date,
    endDate: Date,
    sensorIds?: string[]
  ): Promise<TemperatureReading[]> {
    try {
      console.log(
        `📊 Fetching readings from ${startDate.toISOString()} to ${endDate.toISOString()}`
      );

      let query = supabase
        .from('temperature_readings')
        .select(
          `
          id,
          sensor_id,
          recorded_at,
          created_at,
          temp_celsius,
          temp_fahrenheit,
          humidity,
          battery_level,
          signal_strength,
          data_source,
          alert_triggered
        `
        )
        .gte('recorded_at', startDate.toISOString())
        .lte('recorded_at', endDate.toISOString())
        .order('recorded_at', { ascending: true });

      // Filter by sensor IDs if provided
      if (sensorIds && sensorIds.length > 0) {
        // Convert external sensor IDs to internal UUIDs
        const { data: sensors } = await supabase
          .from('sensors')
          .select('id, sensor_id')
          .in('sensor_id', sensorIds);

        if (sensors && sensors.length > 0) {
          const internalIds = sensors.map((s) => s.id);
          query = query.in('sensor_id', internalIds);
          console.log(
            `🔍 Filtering by ${sensorIds.length} sensor IDs -> ${internalIds.length} internal UUIDs`
          );
        } else {
          console.warn('⚠️ No sensors found for provided sensor IDs:', sensorIds);
          return [];
        }
      }

      const { data: readings, error } = await query;

      if (error) {
        console.error('❌ Database query failed:', error.message);
        throw new TempStickServiceError(
          `Failed to fetch readings: ${error.message}`,
          'getReadingsForDateRange'
        );
      }

      if (!readings) {
        console.log('📊 No readings found for date range');
        return [];
      }

      // Transform to expected format
      const transformedReadings: TemperatureReading[] = readings.map((reading) => {
        const tempF = typeof reading.temp_fahrenheit === 'number'
          ? reading.temp_fahrenheit
          : (typeof reading.temp_celsius === 'number' ? (reading.temp_celsius * 9) / 5 + 32 : Number.NaN);
        return {
          id: reading.id,
          sensor_id: reading.sensor_id, // internal UUID FK
          temperature: tempF,
          humidity: reading.humidity,
          reading_timestamp: reading.recorded_at,
          recorded_at: reading.recorded_at,
          alert_triggered: Boolean(reading.alert_triggered),
          created_at: reading.created_at,
        };
      });

      console.log(`✅ Retrieved ${transformedReadings.length} readings from database`);
      return transformedReadings;
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ getReadingsForDateRange failed:', errorMsg);
      throw new TempStickServiceError(
        `Failed to get readings for date range: ${errorMsg}`,
        'getReadingsForDateRange'
      );
    }
  }

  /**
   * Determine alert level based on temperature (for freezer context)
   */
  private getAlertLevel(tempFahrenheit: number): 'normal' | 'warning' | 'critical' {
    if (tempFahrenheit > 10) return 'critical'; // Above 10°F is critical for frozen food
    if (tempFahrenheit > 0) return 'warning'; // Above 0°F is warning
    return 'normal'; // Below 0°F is normal for freezers
  }

  /**
   * Get service metrics and status
   */
  public getStatus() {
    return {
      apiClient: this.apiClient ? 'initialized' : 'not_initialized',
      syncInProgress: this.syncInProgress,
      lastFullSync: this.lastFullSync?.toISOString() ?? 'never',
      healthChecksEnabled: this.healthCheckInterval !== null,
    };
  }

  /**
   * Clean up resources
   */
  public dispose(): void {
    this.stopHealthChecks();
  }
}

// Create and export default instance
export const tempStickService = new TempStickService({
  enableHealthChecks: true,
});
