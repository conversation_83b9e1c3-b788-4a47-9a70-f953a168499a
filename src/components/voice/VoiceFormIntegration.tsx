import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Mic, MicOff } from 'lucide-react';
import {
  VoiceExtractedData,
  HACCPEventType,
  HACCP_EVENT_TYPES,
  normalizeUnit,
  normalizeTemperatureUnit,
  normalizeCondition,
} from '../../modules/haccp/types';

type ExtendedEventType =
  | HACCPEventType
  | 'order_placed'
  | 'shipping_arrival'
  | 'shipping_departure'
  | 'customer_delivery'
  | 'quality_check'
  | 'temperature_log'
  | 'maintenance';

interface VoiceFormIntegrationProps {
  onDataExtracted: (data: VoiceExtractedData) => void;
  eventType?: ExtendedEventType;
  className?: string;
}

const inferEventType = (text: string): HACCPEventType | undefined => {
  const lower = text.toLowerCase();
  if (lower.includes('disposed') || lower.includes('throw away') || lower.includes('waste')) {
    return 'disposal';
  }
  if (lower.includes('count') || lower.includes('inventory check') || lower.includes('physical count')) {
    return 'physical_count';
  }
  if (lower.includes('sale') || lower.includes('sold to') || lower.includes('invoice') || lower.includes('customer')) {
    return 'sale';
  }
  if (lower.includes('receive') || lower.includes('received') || lower.includes('delivery') || lower.includes('shipment')) {
    return 'receiving';
  }
  return undefined;
};

export default function VoiceFormIntegration({
  onDataExtracted,
  eventType: eventTypeProp,
  className = '',
}: VoiceFormIntegrationProps) {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [feedback, setFeedback] = useState('');
  const [isEnabled, setIsEnabled] = useState(true);

  const resolvedEventType = eventTypeProp ?? 'receiving';

  // Tracks the active SpeechRecognition instance so we can start/stop it imperatively.
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  // Mirrors the latest processing flag for callbacks that fire outside React's render.
  const isProcessingRef = useRef(isProcessing);
  // Store timeout ID for cleanup
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  // Debounce flag to prevent rapid start/stop calls
  const operationInProgressRef = useRef(false);

  useEffect(() => {
    isProcessingRef.current = isProcessing;
  }, [isProcessing]);

  const extractFormData = useCallback(
    (text: string): VoiceExtractedData => {
      // Preserve exactly what the recognizer heard so integrators can log/debug
      // without losing nuance; all normalization happens against derived copies.
      const rawTranscript = text;
      const normalizedTranscript = rawTranscript.toLowerCase();
      const data: Partial<VoiceExtractedData> = {};

      // Words-to-numbers conversion mapping for spelled-out numbers
      const wordToNumber: Record<string, number> = {
        'zero': 0, 'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
        'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10,
        'eleven': 11, 'twelve': 12, 'thirteen': 13, 'fourteen': 14, 'fifteen': 15,
        'sixteen': 16, 'seventeen': 17, 'eighteen': 18, 'nineteen': 19, 'twenty': 20,
        'thirty': 30, 'forty': 40, 'fifty': 50, 'sixty': 60, 'seventy': 70, 'eighty': 80, 'ninety': 90,
        'hundred': 100, 'thousand': 1000
      };

      // Convert spelled-out numbers to digits before regex matching
      let processedTranscript = rawTranscript;

      // Handle compound numbers like "twenty five" -> "25"
      processedTranscript = processedTranscript.replace(/\b(twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety)\s+(one|two|three|four|five|six|seven|eight|nine)\b/gi, (match, tens, ones) => {
        const tensNum = wordToNumber[tens.toLowerCase()] || 0;
        const onesNum = wordToNumber[ones.toLowerCase()] || 0;
        return String(tensNum + onesNum);
      });

      // Handle simple word numbers
      Object.entries(wordToNumber).forEach(([word, num]) => {
        const regex = new RegExp(`\\b${word}\\b`, 'gi');
        processedTranscript = processedTranscript.replace(regex, String(num));
      });

      // Extract quantity and unit with expanded unit list and word boundary support
      const quantityMatch = processedTranscript.match(
        /\b(\d+(?:\.\d+)?)\s*(pounds?|lbs?|lb|kilograms?|kgs?|kg|tons?|ton|tonnes?|tonne|cases?|case|units?|unit|boxes?|box|pieces?|piece|pcs?|pc|ounces?|ozs?|oz|grams?|gms?|gm|g)\b/i
      );
      if (quantityMatch) {
        data.quantity = parseFloat(quantityMatch[1]);
        data.unit = normalizeUnit(quantityMatch[2]);
      }

      // Voice input often mangles seafood terminology; this lookup normalizes
      // common misrecognitions (e.g. "dangerous grab" → "dungeness crab") so downstream
      // matching operates on canonical product names.
      const voiceCorrections: Record<string, string> = {
        'dangerous grab': 'dungeness crab',
        'dangerous crab': 'dungeness crab',
        'king grab': 'king crab',
        'snow grab': 'snow crab',
        'dover soul': 'dover sole',
        'petrel sole': 'petrale sole',
        'see scallops': 'sea scallops',
        'silver salmon': 'coho salmon',
        'king salmon': 'chinook salmon',
        'age and g': 'h&g',
        'head and gutted': 'h&g',
        'i q f': 'iqf',
        'individually quick frozen': 'iqf',
      };

      // Replace every misheard phrase with its canonical equivalent before any
      // further parsing; the regex replacement is case-insensitive so variants
      // like "Dangerous Grab" or "dangerous Grab" are caught.
      let correctedText = normalizedTranscript;
      Object.entries(voiceCorrections).forEach(([wrong, right]) => {
        correctedText = correctedText.replace(new RegExp(wrong, 'gi'), right);
      });
      correctedText = correctedText.toLowerCase();

      // Authoritative list of supported seafood products, grouped for readability.
      // Matching is a simple substring search because call transcripts rarely
      // preserve punctuation; keeping everything lowercase avoids locale issues.
      const seafoodProducts = [
        // Finfish
        'atlantic salmon',
        'coho salmon',
        'chinook salmon',
        'sockeye salmon',
        'pink salmon',
        'atlantic cod',
        'pacific cod',
        'black cod',
        'sablefish',
        'alaska pollock',
        'pacific halibut',
        'atlantic halibut',
        'dover sole',
        'petrale sole',
        'english sole',
        'yellowfin tuna',
        'bluefin tuna',
        'albacore tuna',
        'skipjack tuna',
        'sea bass',
        'striped bass',
        'red snapper',
        'grouper',
        'mahi mahi',
        'swordfish',
        // Shellfish
        'pacific oysters',
        'eastern oysters',
        'blue point oysters',
        'kumamoto oysters',
        'manila clams',
        'littleneck clams',
        'razor clams',
        'geoduck clams',
        'blue mussels',
        'mediterranean mussels',
        'green mussels',
        'sea scallops',
        'bay scallops',
        'diver scallops',
        // Crustaceans
        'maine lobster',
        'spiny lobster',
        'rock lobster',
        'dungeness crab',
        'king crab',
        'snow crab',
        'blue crab',
        'jonah crab',
        'tiger prawns',
        'spot prawns',
        'white shrimp',
        'pink shrimp',
        // Generic terms (for backwards compatibility)
        'cod',
        'salmon',
        'halibut',
        'crab',
        'tuna',
        'shrimp',
        'lobster',
        'scallops',
        'oysters',
        'clams',
        'mussels',
      ];

      for (const product of seafoodProducts) {
        if (correctedText.includes(product)) {
          data.product = product;
          break;
        }
      }

      // Processing method vocabulary used by buyers; voice agents typically
      // report a single keyword (e.g. "IQF" or "previously frozen") so plain
      // substring checks are sufficient.
      const processingMethods = [
        'fresh',
        'frozen',
        'previously frozen',
        'live',
        'iqf',
        'h&g',
        'smoked',
        'cured',
      ];
      for (const method of processingMethods) {
        if (correctedText.includes(method)) {
          data.processingMethod = method;
          break;
        }
      }

      // Market form describes how the product is presented to buyers (fillets,
      // whole, portions, etc.); these terms are stable so we can rely on
      // enumerated matching.
      const marketForms = [
        'whole',
        'fillets',
        'portions',
        'steaks',
        'skin-on',
        'skinless',
        'boneless',
      ];
      for (const form of marketForms) {
        if (correctedText.includes(form)) {
          data.marketForm = form;
          break;
        }
      }

      // Grades capture quality language customers use during calls; include
      // both generic adjectives ("premium") and industry shorthand
      // ("sashimi grade").
      const qualityGrades = [
        'premium',
        'grade a',
        'grade b',
        'sashimi grade',
        'restaurant quality',
      ];
      for (const grade of qualityGrades) {
        if (correctedText.includes(grade)) {
          data.qualityGrade = grade;
          break;
        }
      }

      // Receiving transcripts typically phrase suppliers as "from X"; the
      // non-greedy capture ensures we stop at natural pauses like commas or
      // sentence endings ("from Ocean Harvest, 200 pounds" → vendor="Ocean Harvest").
      if (resolvedEventType === 'receiving') {
        const vendorMatch = rawTranscript.match(/from\s+(.+?)(?:,|\.|$)/i);
        if (vendorMatch) {
          data.vendor = vendorMatch[1].replace(/\s+/g, ' ').trim();
        }
      }

      // Sales calls usually mention the buyer as "to/for X"; capture the name
      // until punctuation so phrases like "to The Wharf, deliver tomorrow" map
      // customer="The Wharf".
      if (resolvedEventType === 'sale') {
        const customerMatch = rawTranscript.match(/(?:to|for)\s+(.+?)(?:,|\.|$)/i);
        if (customerMatch) {
          data.customer = customerMatch[1].replace(/\s+/g, ' ').trim();
        }
      }

      // Condition phrases can appear in various forms like "condition good", "in good condition",
      // "condition is excellent", or "was in poor condition". This flexible regex captures
      // the condition value regardless of phrasing.
      const conditionMatch = rawTranscript.match(
        /(?:(?:in\s+)?(?:(?:condition\s+(?:is\s+|was\s+)?)|(?:(?:is\s+|was\s+)?(?:in\s+)?condition\s+))(excellent|good|fair|poor|damaged))|(?:(excellent|good|fair|poor|damaged)\s+condition)/i
      );
      if (conditionMatch) {
        // Extract the condition value from either capture group
        const conditionValue = conditionMatch[1] || conditionMatch[2];
        const normalizedCondition = normalizeCondition(conditionValue);
        if (normalizedCondition) {
          data.condition = normalizedCondition;
        }
      }

      // Temperature statements can be flexible: "32 F", "32°F", "32 degrees F",
      // "negative 10 celsius", etc. This regex handles optional degree symbols,
      // optional "degrees" word, and various unit forms.
      const tempMatch = rawTranscript.match(
        /((?:negative\s+|-)?((\d+(?:\.\d+)?)))\s*(?:degrees?|°)?\s*(fahrenheit|celsius|f|c)?/i
      );
      if (tempMatch) {
        // Handle negative temperatures
        let tempValue = parseFloat(tempMatch[2]);
        if (tempMatch[1].toLowerCase().includes('negative') || tempMatch[1].includes('-')) {
          tempValue = -tempValue;
        }
        data.temperature = tempValue;

        // Use provided unit or default to fahrenheit if none specified
        const unit = tempMatch[4] || 'f';
        data.temperatureUnit = normalizeTemperatureUnit(unit);
      }

      // Transcripts may include free-form notes prefixed by connectors such as
      // "note:" or "comment"; this helper grabs everything after the keyword,
      // strips leading punctuation, and returns the remaining narrative.
      const extractNotes = (source: string, keyword: string): string | undefined => {
        const lowerSource = source.toLowerCase();
        const idx = lowerSource.indexOf(keyword);
        if (idx === -1) return undefined;
        const remainder = source.slice(idx + keyword.length);
        const cleaned = remainder.replace(/^[:\s]+/, '').trim();
        return cleaned.length > 0 ? cleaned : undefined;
      };

      // Allow any of these cue words to introduce notes, covering common
      // variants heard in warehouse or dockside chatter. Includes plural forms and variants.
      const notesKeywords = [
        'note', 'notes', 'noted',
        'comment', 'comments',
        'remark', 'remarks',
        'observation', 'observations', 'obs'
      ];
      for (const keyword of notesKeywords) {
        const note = extractNotes(rawTranscript, keyword);
        if (note) {
          data.notes = note;
          break;
        }
      }

      const explicitEventType =
        eventTypeProp && HACCP_EVENT_TYPES.includes(eventTypeProp as HACCPEventType)
          ? (eventTypeProp as HACCPEventType)
          : undefined;
      const inferredEventType = inferEventType(correctedText);

      // Favor the explicit eventType prop when it maps to the HACCP domain so
      // integrators stay in control; only fall back to inference for legacy
      // consumers that omit the prop, ensuring transcripts like "disposed 10 pounds"
      // still resolve to reasonable defaults.
      data.eventType =
        explicitEventType ?? inferredEventType ?? ('receiving' as HACCPEventType);

      return data as VoiceExtractedData;
    },
    [eventTypeProp, resolvedEventType]
  );
  useEffect(() => {
    type SpeechRecognitionConstructor = new () => SpeechRecognition;

    const { webkitSpeechRecognition, SpeechRecognition: NativeSpeechRecognition } =
      window as typeof window & {
        webkitSpeechRecognition?: SpeechRecognitionConstructor;
        SpeechRecognition?: SpeechRecognitionConstructor;
      };

    const SpeechRecognitionClass = webkitSpeechRecognition ?? NativeSpeechRecognition;

    if (!SpeechRecognitionClass) {
      setIsEnabled(false);
      return;
    }

    const recognitionInstance = new SpeechRecognitionClass();
    recognitionInstance.continuous = false;
    recognitionInstance.interimResults = false;
    recognitionInstance.lang = 'en-US';

    recognitionInstance.onstart = () => {
      setIsListening(true);
      setFeedback('Listening for form data...');
      operationInProgressRef.current = false;
    };

    recognitionInstance.onend = () => {
      setIsListening(false);
      operationInProgressRef.current = false;
      if (!isProcessingRef.current) {
        setFeedback('');
      }
    };

    recognitionInstance.onresult = async (event) => {
      const transcriptResult = event.results?.[0]?.[0]?.transcript ?? '';
      const trimmedTranscript = transcriptResult.trim();

      if (!trimmedTranscript) {
        isProcessingRef.current = false;
        setTranscript('');
        setFeedback('No speech detected. Please try again.');
        setIsProcessing(false);
        return;
      }

      setTranscript(trimmedTranscript);
      isProcessingRef.current = true;
      setIsProcessing(true);
      setFeedback('Extracting data...');

      try {
        const extractedData = extractFormData(trimmedTranscript);
        onDataExtracted(extractedData);
        setFeedback('Form data extracted successfully!');
      } catch (error) {
        console.error('Voice extraction error:', error);
        setFeedback('Error extracting form data. Please try again.');
      } finally {
        isProcessingRef.current = false;
        setIsProcessing(false);
        timeoutRef.current = setTimeout(() => {
          setFeedback('');
          setTranscript('');
        }, 3000);
      }
    };

    recognitionInstance.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      isProcessingRef.current = false;
      operationInProgressRef.current = false;
      setIsListening(false);
      setIsProcessing(false);
      setFeedback('Voice recognition error. Please try again.');
    };

    recognitionRef.current = recognitionInstance;

    return () => {
      // Clear timeout if component unmounts
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      // Remove event handlers to prevent memory leaks
      recognitionInstance.onstart = null;
      recognitionInstance.onend = null;
      recognitionInstance.onresult = null;
      recognitionInstance.onerror = null;

      recognitionInstance.abort();
      recognitionRef.current = null;
    };
  }, [extractFormData, onDataExtracted]);

  const startListening = useCallback(() => {
    if (recognitionRef.current && isEnabled && !isListening && !operationInProgressRef.current) {
      operationInProgressRef.current = true;
      setTranscript('');
      setFeedback('');

      try {
        recognitionRef.current.start();
      } catch (error) {
        console.error('Failed to start speech recognition:', error);
        setFeedback('Failed to start voice recognition. Please try again.');
        operationInProgressRef.current = false;
      }
    }
  }, [isEnabled, isListening]);

  const stopListening = useCallback(() => {
    if (recognitionRef.current && isListening && !operationInProgressRef.current) {
      operationInProgressRef.current = true;

      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.error('Failed to stop speech recognition:', error);
        setFeedback('Failed to stop voice recognition.');
        setIsListening(false);
        operationInProgressRef.current = false;
      }
    }
  }, [isListening]);

  if (!isEnabled) {
    return null; // Don't show anything if voice is not supported
  }

  const getPromptText = () => {
    switch (resolvedEventType) {
      case 'receiving':
        return 'Say something like: "50 pounds salmon from Pacific Seafoods, condition excellent"';
      case 'disposal':
        return 'Say something like: "10 pounds expired cod, reason spoilage"';
      case 'physical_count':
        return 'Say something like: "75 pounds cod in freezer"';
      case 'sale':
        return 'Say something like: "30 pounds salmon to Restaurant ABC"';
      default:
        return 'Describe the inventory details you want to add';
    }
  };

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-center gap-3 mb-2">
        <button
          onClick={isListening ? stopListening : startListening}
          disabled={isProcessing}
          className={`p-2 rounded-full transition-all ${
            isListening
              ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          } ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isListening ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
        </button>

        <div className="flex-1">
          <div className="text-sm font-medium text-blue-900">Voice Form Fill</div>
          <div className="text-xs text-blue-700">
            {isListening ? 'Listening...' : isProcessing ? 'Processing...' : 'Click to speak'}
          </div>
        </div>
      </div>

      {transcript && (
        <div className="bg-white rounded p-2 text-sm text-gray-900 mb-2">
          <strong>Heard:</strong> "{transcript}"
        </div>
      )}

      {feedback && (
        <div className="bg-blue-100 rounded p-2 text-sm text-blue-800 mb-2">{feedback}</div>
      )}

      <div className="text-xs text-blue-600">
        <strong>Try saying:</strong> {getPromptText()}
      </div>
    </div>
  );
}
