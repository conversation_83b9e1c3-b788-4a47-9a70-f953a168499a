import { VoiceEventResult } from '../types/schema';

/**
 * Comprehensive error types for voice processing
 */
export enum VoiceErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUDIO_PROCESSING_ERROR = 'AUDIO_PROCESSING_ERROR',
  TRANSCRIPTION_ERROR = 'TRANSCRIPTION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface VoiceError {
  type: VoiceErrorType;
  message: string;
  originalError?: Error;
  code?: string;
  recoverable: boolean;
  userMessage: string;
  actionSteps: string[];
  retryAfter?: number; // seconds
}

export interface QueuedVoiceEvent {
  id: string;
  audioBlob: Blob;
  userId?: string;
  timestamp: string;
  attempts: number;
  lastAttempt?: string;
  error?: VoiceError;
}

/**
 * Comprehensive error handling and recovery service for voice processing
 */
export class VoiceErrorHandler {
  private offlineQueue: QueuedVoiceEvent[] = [];
  private isOnline: boolean = navigator.onLine;
  private retryTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private maxQueueSize: number = 100;
  private maxRetryAttempts: number = 3;

  constructor() {
    // Listen for online/offline events
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));

    // Load persisted queue from localStorage
    this.loadOfflineQueue();
  }

  /**
   * Process voice error and determine recovery strategy
   */
  handleVoiceError(error: unknown, _context?: Record<string, unknown>): VoiceError {
    let voiceError: VoiceError;

    if (error instanceof TypeError && error.message.includes('fetch')) {
      voiceError = {
        type: VoiceErrorType.NETWORK_ERROR,
        message: 'Network connection failed',
        originalError: error as Error,
        recoverable: true,
        userMessage: 'Unable to connect to voice processing service',
        actionSteps: [
          'Check your internet connection',
          'Voice input has been saved and will be processed when connection is restored',
          'You can continue working offline',
        ],
        retryAfter: 30,
      };
    } else if (error instanceof Error && /Unknown parameter: 'session\.type'/.test(error.message)) {
      voiceError = {
        type: VoiceErrorType.VALIDATION_ERROR,
        message: 'Realtime session negotiation failed',
        originalError: error,
        recoverable: true,
        userMessage: 'The voice session could not be negotiated with the realtime service.',
        actionSteps: [
          'Retry with WebSocket fallback',
          'Update client to send session.update after connect',
          'Confirm the client SDK version matches realtime API requirements',
        ],
      };
    } else if (error instanceof Error && error.message.includes('audio')) {
      voiceError = {
        type: VoiceErrorType.AUDIO_PROCESSING_ERROR,
        message: 'Audio processing failed',
        originalError: error,
        recoverable: true,
        userMessage: 'Voice input could not be processed',
        actionSteps: [
          'Check your microphone permissions',
          'Ensure your microphone is working properly',
          'Try speaking closer to your microphone',
          'Use manual input as an alternative',
        ],
      };
    } else if (error instanceof Error && error.message.includes('transcription')) {
      voiceError = {
        type: VoiceErrorType.TRANSCRIPTION_ERROR,
        message: 'Speech-to-text conversion failed',
        originalError: error,
        recoverable: true,
        userMessage: 'Could not understand your voice input',
        actionSteps: [
          'Try speaking more clearly',
          'Speak closer to your microphone',
          'Check for background noise',
          'Use manual input instead',
        ],
      };
    } else if (error instanceof Error && error.message.includes('database')) {
      voiceError = {
        type: VoiceErrorType.DATABASE_ERROR,
        message: 'Database operation failed',
        originalError: error,
        recoverable: true,
        userMessage: 'Could not save voice input',
        actionSteps: [
          'Voice input has been queued for retry',
          'Check your internet connection',
          'Data will be saved automatically when connection is restored',
        ],
        retryAfter: 10,
      };
    } else if (error instanceof Error && error.message.includes('timeout')) {
      voiceError = {
        type: VoiceErrorType.TIMEOUT_ERROR,
        message: 'Voice processing timed out',
        originalError: error,
        recoverable: true,
        userMessage: 'Voice processing is taking too long',
        actionSteps: [
          'Try again with a shorter voice input',
          'Check your internet connection speed',
          'Use manual input if the problem persists',
        ],
        retryAfter: 5,
      };
    } else if (error instanceof Error && error.message.includes('permission')) {
      voiceError = {
        type: VoiceErrorType.PERMISSION_ERROR,
        message: 'Microphone permission denied',
        originalError: error,
        recoverable: false,
        userMessage: 'Microphone access is required for voice input',
        actionSteps: [
          "Click the microphone icon in your browser's address bar",
          'Select "Allow" to enable microphone access',
          'Refresh the page if needed',
          'Use manual input as an alternative',
        ],
      };
    } else {
      voiceError = {
        type: VoiceErrorType.UNKNOWN_ERROR,
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        originalError: error instanceof Error ? error : undefined,
        recoverable: true,
        userMessage: 'An unexpected error occurred during voice processing',
        actionSteps: [
          'Try your voice input again',
          'Use manual input as an alternative',
          'Contact support if the problem persists',
        ],
      };
    }

    console.error('Voice processing error:', voiceError);
    return voiceError;
  }

  /**
   * Queue voice event for offline processing
   */
  async queueOfflineEvent(audioBlob: Blob, userId?: string): Promise<string> {
    const queuedEvent: QueuedVoiceEvent = {
      id: crypto.randomUUID(),
      audioBlob,
      userId,
      timestamp: new Date().toISOString(),
      attempts: 0,
    };

    // Remove oldest events if queue is full
    if (this.offlineQueue.length >= this.maxQueueSize) {
      this.offlineQueue.shift();
    }

    this.offlineQueue.push(queuedEvent);
    this.persistOfflineQueue();

    console.log(`Queued voice event ${queuedEvent.id} for offline processing`);
    return queuedEvent.id;
  }

  /**
   * Process queued events when connection is restored
   */
  async processQueuedEvents(): Promise<void> {
    if (!this.isOnline || this.offlineQueue.length === 0) {
      return;
    }

    console.log(`Processing ${this.offlineQueue.length} queued voice events`);

    // Process events one by one to avoid overwhelming the system
    const eventsToProcess = [...this.offlineQueue];
    this.offlineQueue = [];

    for (const queuedEvent of eventsToProcess) {
      try {
        await this.retryQueuedEvent(queuedEvent);
      } catch (error) {
        console.error(`Failed to process queued event ${queuedEvent.id}:`, error);

        // Re-queue if recoverable and under retry limit
        if (queuedEvent.attempts < this.maxRetryAttempts) {
          queuedEvent.attempts++;
          queuedEvent.lastAttempt = new Date().toISOString();
          queuedEvent.error = this.handleVoiceError(error);
          this.offlineQueue.push(queuedEvent);
        }
      }
    }

    this.persistOfflineQueue();
  }

  /**
   * Retry a specific queued event
   */
  private async retryQueuedEvent(queuedEvent: QueuedVoiceEvent): Promise<VoiceEventResult> {
    // Import here to avoid circular dependency
    const { enhancedVoiceEventProcessor } = await import('./EnhancedVoiceEventProcessor');
    return enhancedVoiceEventProcessor.processVoiceEvent(queuedEvent.audioBlob, queuedEvent.userId);
  }

  /**
   * Handle online event
   */
  private handleOnline(): void {
    console.log('Connection restored - processing queued voice events');
    this.isOnline = true;
    this.processQueuedEvents();
  }

  /**
   * Handle offline event
   */
  private handleOffline(): void {
    console.log('Connection lost - voice events will be queued');
    this.isOnline = false;
  }

  /**
   * Persist offline queue to localStorage
   */
  private persistOfflineQueue(): void {
    try {
      // Convert Blob to base64 for storage
      const queueData = this.offlineQueue.map((event) => ({
        ...event,
        audioBlob: undefined, // Remove blob for now, implement proper serialization later
        hasAudio: true,
      }));

      localStorage.setItem('voiceEventQueue', JSON.stringify(queueData));
    } catch (error) {
      console.error('Failed to persist offline queue:', error);
    }
  }

  /**
   * Load offline queue from localStorage
   */
  private loadOfflineQueue(): void {
    try {
      const queueData = localStorage.getItem('voiceEventQueue');
      if (queueData) {
        // Note: Audio blobs can't be persisted, so we'll need to handle this differently
        // For now, just clear the persisted queue on app restart
        localStorage.removeItem('voiceEventQueue');
      }
    } catch (error) {
      console.error('Failed to load offline queue:', error);
    }
  }

  /**
   * Get current queue status
   */
  getQueueStatus(): {
    isOnline: boolean;
    queuedEvents: number;
    failedEvents: number;
  } {
    const failedEvents = this.offlineQueue.filter((event) => event.error).length;

    return {
      isOnline: this.isOnline,
      queuedEvents: this.offlineQueue.length,
      failedEvents,
    };
  }

  /**
   * Clear all queued events
   */
  clearQueue(): void {
    this.offlineQueue = [];
    this.persistOfflineQueue();

    // Clear any pending retries
    for (const timeout of this.retryTimeouts.values()) {
      clearTimeout(timeout);
    }
    this.retryTimeouts.clear();
  }

  /**
   * Get user-friendly error message for display
   */
  getErrorMessage(error: VoiceError): string {
    return `${error.userMessage}\n\n${error.actionSteps.join('\n')}`;
  }

  /**
   * Check if error is recoverable
   */
  isRecoverable(error: VoiceError): boolean {
    return error.recoverable;
  }

  /**
   * Schedule retry for recoverable errors
   */
  scheduleRetry(eventId: string, retryFunction: () => Promise<void>, delay: number = 5000): void {
    const existingTimeout = this.retryTimeouts.get(eventId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    const timeout = setTimeout(async () => {
      try {
        await retryFunction();
        this.retryTimeouts.delete(eventId);
      } catch (error) {
        console.error(`Scheduled retry failed for event ${eventId}:`, error);
      }
    }, delay);

    this.retryTimeouts.set(eventId, timeout);
  }
}

export const voiceErrorHandler = new VoiceErrorHandler();
